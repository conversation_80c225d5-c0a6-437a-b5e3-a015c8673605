package com.bxm.customer.domain.dto.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class AccountingCashierBankStatisticDTO {

    @ApiModelProperty("未开户数量")
    private Long unOpenedCount;

    @ApiModelProperty("银行部分缺数量")
    private Long bankPartialMissingCount;

    @ApiModelProperty("待创建数量")
    private Long waitCreateCount;

    @ApiModelProperty("待重提数量")
    private Long waitResubmitCount;

    @ApiModelProperty("待交付数量")
    private Long waitDeliverCount;

    @ApiModelProperty("异常数量")
    private Long exceptionCount;

    @ApiModelProperty("有变更数量")
    private Long hasChangedCount;

    @ApiModelProperty("缺材料数量")
    private Long lackOfMaterialCount;

    @ApiModelProperty("交付待提交数量")
    private Long waitSubmitCount;

    @ApiModelProperty("待顾问创建数量")
    private Long waitAdvisorCreateCount;

    @ApiModelProperty("待回单中心创建数量")
    private Long waitReceiptCreateCount;

    @ApiModelProperty("银企待创建数量")
    private Long waitBankCompanyCreateCount;

    @ApiModelProperty("异常待顾问处理数量")
    private Long waitAdvisorDealCount;

    @ApiModelProperty("异常待回单中心处理数量")
    private Long waitReceiptDealCount;

    @ApiModelProperty("异常待流信部数量")
    private Long waitBankCompanyDealCount;

    public AccountingCashierBankStatisticDTO() {
        this.unOpenedCount = 0L;
        this.bankPartialMissingCount = 0L;
        this.waitCreateCount = 0L;
        this.waitResubmitCount = 0L;
        this.waitDeliverCount = 0L;
        this.exceptionCount = 0L;
        this.hasChangedCount = 0L;
        this.lackOfMaterialCount = 0L;
        this.waitSubmitCount = 0L;
        this.waitAdvisorCreateCount = 0L;
        this.waitReceiptCreateCount = 0L;
        this.waitBankCompanyCreateCount = 0L;
        this.waitAdvisorDealCount = 0L;
        this.waitReceiptDealCount = 0L;
        this.waitBankCompanyDealCount = 0L;
    }
}

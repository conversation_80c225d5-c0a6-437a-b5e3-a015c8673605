package com.bxm.customer.domain.vo.valueAdded;

import com.bxm.common.core.annotation.OnlyIgnoreKey;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 国税账号VO
 *
 * 用于展示国税账号的详细信息，字段名称与业务表单保持一致
 * 继承BaseTaxAccountVO，包含国税账号特有的业务逻辑
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("国税账号VO")
public class NationalTaxAccountVO extends BaseTaxAccountVO {

    /** 操作方式：1-账号信息，2-会计实名 */
    @ApiModelProperty(value = "账号类型",notes = "1-账号信息，2-会计实名")
    private Integer operationType;

    /** 操作方式名称 */
    @ApiModelProperty(value = "信息", notes = "1-账号信息，2-会计实名")
    @OnlyIgnoreKey
    private String operationTypeName;
}

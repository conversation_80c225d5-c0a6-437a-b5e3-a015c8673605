package com.bxm.customer.utils;

import com.bxm.common.core.utils.StringUtils;

import java.util.regex.Pattern;

/**
 * 通用校验工具类
 *
 * 提供与业务无关的基础格式校验功能，包括：
 * 1. 手机号格式校验
 * 2. 身份证号校验（只校验是否为空）
 * 3. 其他通用格式校验
 *
 * <AUTHOR>
 * @date 2025-08-23
 */
public class ValidateUtil {

    // 手机号正则：11位数字
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^\\d{11}$");
    
    // 身份证号长度校验：18个字符
    private static final int ID_NUMBER_LENGTH = 18;

    // ==================== 手机号校验 ====================

    /**
     * 校验手机号格式
     * 规则：必填，11个数字
     *
     * @param mobile 手机号
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            throw new IllegalArgumentException("手机号不能为空");
        }
        
        String trimmedMobile = mobile.trim();
        if (!MOBILE_PATTERN.matcher(trimmedMobile).matches()) {
            throw new IllegalArgumentException("手机号必须为11位数字");
        }
    }

    /**
     * 检查手机号格式是否正确
     *
     * @param mobile 手机号
     * @return 是否格式正确
     */
    public static boolean isValidMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        return MOBILE_PATTERN.matcher(mobile.trim()).matches();
    }

    // ==================== 身份证号校验 ====================

    /**
     * 校验身份证号
     * 规则：必填，18个字符
     *
     * @param idNumber 身份证号
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateIdNumberStrict(String idNumber) {
        if (StringUtils.isEmpty(idNumber)) {
            throw new IllegalArgumentException("身份证号不能为空");
        }
        
        String trimmedIdNumber = idNumber.trim();
        if (trimmedIdNumber.length() != ID_NUMBER_LENGTH) {
            throw new IllegalArgumentException("身份证号必须为18位字符");
        }
    }

    /**
     * 校验身份证号
     * 
     * @param idNumber 身份证号
     * @param allowEmpty 是否允许为空
     * @return 验证结果
     */
    public static boolean isValidIdNumber(String idNumber, boolean allowEmpty) {
        // 如果允许为空且值为空，则验证通过
        if (allowEmpty && StringUtils.isEmpty(idNumber)) {
            return true;
        }

        // 如果不允许为空且值为空，则验证失败
        if (!allowEmpty && StringUtils.isEmpty(idNumber)) {
            return false;
        }

        // 校验长度是否为18个字符
        String trimmedIdNumber = idNumber.trim();
        return trimmedIdNumber.length() == ID_NUMBER_LENGTH;
    }

    /**
     * 校验身份证号（不允许为空）
     * 
     * @param idNumber 身份证号
     * @return 验证结果
     */
    public static boolean isValidIdNumber(String idNumber) {
        return isValidIdNumber(idNumber, false);
    }

    /**
     * 验证身份证号并抛出异常
     * 
     * @param idNumber 身份证号
     * @param fieldName 字段名称，用于错误信息
     * @throws IllegalArgumentException 当身份证号为空或格式不正确时抛出
     */
    public static void validateIdNumberAndThrow(String idNumber, String fieldName) {
        if (StringUtils.isEmpty(idNumber)) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
        
        String trimmedIdNumber = idNumber.trim();
        if (trimmedIdNumber.length() != ID_NUMBER_LENGTH) {
            throw new IllegalArgumentException(fieldName + "必须为18位字符");
        }
    }

    /**
     * 验证身份证号并抛出异常（使用默认字段名）
     * 
     * @param idNumber 身份证号
     * @throws IllegalArgumentException 当身份证号为空或格式不正确时抛出
     */
    public static void validateIdNumberAndThrow(String idNumber) {
        validateIdNumberAndThrow(idNumber, "身份证号");
    }

}

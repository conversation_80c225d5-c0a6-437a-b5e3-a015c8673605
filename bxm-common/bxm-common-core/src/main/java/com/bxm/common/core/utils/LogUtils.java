package com.bxm.common.core.utils;

import com.bxm.common.core.utils.logstring.*;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;


/**
 * 日志工具类
 * 用于将对象转换为日志字符串，基于 @ApiModelProperty 注解的 value 值作为键名
 * 支持多种输出格式、敏感数据掩码、缓存等高级功能
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
public class LogUtils {

    private static final FieldProcessor fieldProcessor = new FieldProcessor();
    private static final LogStringConfig defaultConfig = LogStringConfig.builder()
            .skipEmptyObjects(true)
            .skipNullValues(true)
            .skipEmptyStrings(true)
            .maskSensitiveFields(false)
            .build();

    /**
     * 将对象转换为日志字符串（使用默认配置）
     * 基于 @ApiModelProperty 注解的 value 值作为键名，对象字段值作为值
     * 对于 Boolean 类型，自动转换为 "是" 或 "否"
     *
     * @param obj 要转换的对象
     * @return 格式化的日志字符串，如：客户企业名称: 测试公司, 是否同步汇算: 是
     */
    public static String toLogString(Object obj) {
        return toLogString(obj, defaultConfig);
    }

    /**
     * 将对象转换为日志字符串（使用自定义配置）
     *
     * @param obj    要转换的对象
     * @param config 配置对象
     * @return 格式化的日志字符串
     */
    public static String toLogString(Object obj, LogStringConfig config) {
        if (obj == null) {
            return "";
        }

        if (config == null) {
            config = defaultConfig;
        }

        try {
            return processObject(obj, config, 0);
        } catch (Exception e) {
            // 发生异常时使用简单的 toString 作为回退
            return obj.toString();
        }
    }

    /**
     * 将对象转换为日志字符串，忽略指定字段（使用字段名）
     *
     * @param obj          要转换的对象
     * @param ignoreFields 需要忽略的字段名集合
     * @return 格式化的日志字符串
     */
    public static String toLogString(Object obj, Set<String> ignoreFields) {
        LogStringConfig config = LogStringConfig.builder()
                .skipEmptyObjects(defaultConfig.isSkipEmptyObjects())
                .classSpecificIgnoreFields(defaultConfig.getClassSpecificIgnoreFields())
                .enableIgnoreFields(true)
                .ignoreFieldNames(ignoreFields)
                .build();
        return toLogString(obj, config);
    }

    /**
     * 将对象转换为日志字符串，忽略指定字段（使用方法引用）
     * <p>
     * 使用示例：
     * <pre>
     * User user = new User();
     * String log = LogUtils.toLogString(user, User::getPassword, User::getPhone);
     * </pre>
     *
     * @param obj             要转换的对象
     * @param ignoreFieldRefs 需要忽略的字段方法引用
     * @return 格式化的日志字符串
     */
    @SafeVarargs
    public static <T> String toLogString(T obj, FieldReference<T, ?>... ignoreFieldRefs) {
        if (ignoreFieldRefs == null || ignoreFieldRefs.length == 0) {
            return toLogString(obj);
        }

        Set<String> ignoreFields = FieldReferenceUtils.getFieldNames(ignoreFieldRefs);
        return toLogString(obj, ignoreFields);
    }

    /**
     * 将对象转换为日志字符串，支持字段值覆盖
     * <p>
     * 使用示例：
     * <pre>
     * User user = new User();
     * Map&lt;FieldReference&lt;User, ?&gt;, Function&lt;User, String&gt;&gt; overrides = new HashMap&lt;&gt;();
     * overrides.put(User::getName, u -&gt; "自定义名称");
     * String log = LogUtils.toLogString(user, overrides);
     * </pre>
     *
     * @param obj            要转换的对象
     * @param fieldOverrides 字段覆盖映射，key为字段引用，value为值提供函数
     * @return 格式化的日志字符串
     */
    public static <T> String toLogString(T obj, LogStringConfig config, Map<FieldReference<T, ?>, Function<T, String>> fieldOverrides) {
        if (obj == null) {
            return "";
        }
        if (fieldOverrides == null || fieldOverrides.isEmpty()) {
            return toLogString(obj);
        }

        LogStringConfig configDefault = LogStringConfig.builder()
                .skipEmptyObjects(config.isSkipEmptyObjects())
                .classSpecificIgnoreFields(config.getClassSpecificIgnoreFields())
                .enableFieldOverrides(true)
                .format(config.getFormat())
                .fieldOverrides(convertFieldOverrides(obj, fieldOverrides))
                .build();

        return toLogString(obj, configDefault);
    }

    /**
     * 将对象转换为日志字符串，同时支持字段忽略和字段值覆盖
     * <p>
     * 使用示例：
     * <pre>
     * User user = new User();
     * Map&lt;FieldReference&lt;User, ?&gt;, Function&lt;User, String&gt;&gt; overrides = new HashMap&lt;&gt;();
     * overrides.put(User::getTaxpayerType, u -&gt; u.getTaxpayerType() == 1 ? "小规模纳税人" : "一般纳税人");
     * String log = LogUtils.toLogString(user, overrides, User::getPassword, User::getPhone);
     * </pre>
     *
     * @param obj             要转换的对象
     * @param fieldOverrides  字段覆盖映射，key为字段引用，value为值提供函数
     * @param ignoreFieldRefs 需要忽略的字段方法引用
     * @return 格式化的日志字符串
     */
    @SafeVarargs
    public static <T> String toLogString(T obj, Map<FieldReference<T, ?>, Function<T, String>> fieldOverrides, FieldReference<T, ?>... ignoreFieldRefs) {
        return toLogString(obj, defaultConfig, fieldOverrides, ignoreFieldRefs);
    }

    @SafeVarargs
    public static <T> String toLogString(T obj, LogStringConfig customerConfig, Map<FieldReference<T, ?>, Function<T, String>> fieldOverrides, FieldReference<T, ?>... ignoreFieldRefs) {
        if (obj == null) {
            return "";
        }
        // 创建配置构建器，继承默认配置
        LogStringConfig.Builder configBuilder = LogStringConfig.builder()
                .skipEmptyObjects(customerConfig.isSkipEmptyObjects())
                .format(customerConfig.getFormat())
                .classSpecificIgnoreFields(customerConfig.getClassSpecificIgnoreFields());

        // 处理字段覆盖
        if (fieldOverrides != null && !fieldOverrides.isEmpty()) {
            configBuilder.enableFieldOverrides(true)
                    .fieldOverrides(convertFieldOverrides(obj, fieldOverrides));
        }

        // 处理字段忽略
        if (ignoreFieldRefs != null && ignoreFieldRefs.length > 0) {
            Set<String> ignoreFields = FieldReferenceUtils.getFieldNames(ignoreFieldRefs);
            configBuilder.enableIgnoreFields(true).ignoreFieldNames(ignoreFields);
        }

        LogStringConfig config = configBuilder.build();
        return toLogString(obj, config);
    }

    /**
     * 转换字段覆盖映射为内部使用的格式
     *
     * @param obj            对象实例
     * @param fieldOverrides 字段覆盖映射
     * @return 转换后的字段覆盖映射
     */
    private static <T> Map<String, String> convertFieldOverrides(T obj, Map<FieldReference<T, ?>, Function<T, String>> fieldOverrides) {
        Map<String, String> result = new HashMap<>();

        for (Map.Entry<FieldReference<T, ?>, Function<T, String>> entry : fieldOverrides.entrySet()) {
            try {
                // 获取字段名
                Set<String> fieldNames = FieldReferenceUtils.getFieldNames(entry.getKey());
                String fieldName = fieldNames.iterator().next(); // 取第一个字段名

                // 计算覆盖值
                String overrideValue = entry.getValue().apply(obj);

                result.put(fieldName, overrideValue);
            } catch (Exception e) {
                // 忽略转换失败的字段覆盖
            }
        }

        return result;
    }

    /**
     * 创建建造者
     *
     * @return LogStringBuilder 实例
     */
    public static LogStringBuilder builder() {
        return new LogStringBuilder();
    }

    /**
     * 处理对象（公共方法，供ValueFormatter调用）
     *
     * @param obj    对象
     * @param config 配置
     * @param depth  当前深度
     * @return 格式化字符串
     */
    public static String processObject(Object obj, LogStringConfig config, int depth) {
        if (obj == null) {
            return "";
        }

        if (depth >= config.getMaxDepth()) {
            return obj.toString();
        }

        List<String> logParts = new ArrayList<>();
        Class<?> clazz = obj.getClass();

        // 提取字段信息
        List<FieldInfo> fieldInfos = fieldProcessor.extractFields(clazz, config);

        // 用于检查是否所有字段都为空（当启用skipEmptyObjects时）
        boolean hasNonEmptyField = false;

        for (FieldInfo fieldInfo : fieldInfos) {
            try {
                Field field = fieldInfo.getField();
                field.setAccessible(true);
                Object value = field.get(obj);

                // 优先检查是否有字段覆盖值，如果有覆盖值则不跳过该字段
                boolean hasOverride = config.isEnableFieldOverrides() &&
                        config.getFieldOverrides().containsKey(fieldInfo.getOriginalName());

                // 如果没有覆盖值，才根据配置决定是否跳过空值
                if (!hasOverride) {
                    if (config.isSkipNullValues() && value == null) {
                        continue;
                    }
                    if (config.isSkipEmptyStrings() && value instanceof String && ((String) value).isEmpty()) {
                        continue;
                    }
                }

                // 标记存在字段（包括空值字段，因为它们也是有意义的状态）
                hasNonEmptyField = true;

                String formattedField = fieldProcessor.formatField(fieldInfo, value, config);
                // 只有当格式化结果不为空时才添加到日志中
                if (StringUtils.isNotBlank(formattedField)) {
                    logParts.add(formattedField);
                }

            } catch (IllegalAccessException e) {
                throw new RuntimeException("无法访问字段: ", e);
            }
        }

        // 如果启用了skipEmptyObjects且所有字段都为空，返回空字符串
        if (config.isSkipEmptyObjects() && !hasNonEmptyField) {
            return "";
        }

        return joinLogParts(logParts, config);
    }

    /**
     * 连接日志部分
     *
     * @param logParts 日志部分列表
     * @param config   配置
     * @return 连接后的字符串
     */
    private static String joinLogParts(List<String> logParts, LogStringConfig config) {
        if (logParts.isEmpty()) {
            return "";
        }

        switch (config.getFormat()) {
            case JSON_LIKE:
                return "{" + String.join(", ", logParts) + "}";

            case STRUCTURED:
                return String.join("|", logParts);

            case MULTILINE:
                return String.join("\n", logParts);

            case KEY_VALUE:
            default:
                return String.join(config.getSeparator(), logParts);
        }
    }

    /**
     * 清除缓存
     */
    public static void clearCache() {
        FieldProcessor.clearCache();
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存中的类数量
     */
    public static int getCacheSize() {
        return FieldProcessor.getCacheSize();
    }


}

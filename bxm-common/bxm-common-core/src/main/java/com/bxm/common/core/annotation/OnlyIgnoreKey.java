package com.bxm.common.core.annotation;

import java.lang.annotation.*;

/**
 * 日志输出只显示字段值的注解
 * 标记在字段上时，toLogString 方法将只输出字段的实际值，不输出键值对形式
 * 适用于需要在日志中只显示字段内容而不显示字段描述的场景
 *
 * 使用示例：
 * <pre>
 * &#64;ApiModelProperty(value = "信息", notes = "1-账号信息，2-会计实名")
 * &#64;OnlyIgnoreKey
 * private String operationTypeName; // 假设值为"账号信息"
 * </pre>
 *
 * 在使用 LogUtils.toLogString() 时，该字段将输出为：账号信息
 * 而不是：信息: 账号信息
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OnlyIgnoreKey {

}

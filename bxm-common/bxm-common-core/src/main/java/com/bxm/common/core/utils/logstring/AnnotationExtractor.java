package com.bxm.common.core.utils.logstring;

import com.bxm.common.core.annotation.OnlyIgnoreKey;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Set;

/**
 * 注解提取器
 * 用于处理 @ApiModelProperty 注解和敏感字段检测
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
public class AnnotationExtractor {

    /**
     * 提取字段的中文标签
     * 优先使用 @ApiModelProperty 的 value 属性，如果没有则使用字段名
     *
     * @param field 字段对象
     * @return 中文标签或字段名
     */
    public String extractChineseLabel(Field field) {
        if (field == null) {
            return "";
        }

        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
        if (annotation != null && StringUtils.isNotBlank(annotation.value())) {
            return annotation.value();
        }

        return field.getName();
    }

    /**
     * 检查字段是否为敏感字段
     * 基于字段名和配置的敏感字段模式进行判断
     *
     * @param field 字段对象
     * @param sensitiveFieldNames 敏感字段名称集合
     * @return 是否为敏感字段
     */
    public boolean isSensitiveField(Field field, Set<String> sensitiveFieldNames) {
        if (field == null || sensitiveFieldNames == null || sensitiveFieldNames.isEmpty()) {
            return false;
        }

        String fieldName = field.getName().toLowerCase();

        // 检查是否匹配敏感字段模式
        for (String sensitivePattern : sensitiveFieldNames) {
            if (fieldName.contains(sensitivePattern.toLowerCase())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 提取字段描述信息
     * 从 @ApiModelProperty 的 notes 属性获取描述
     *
     * @param field 字段对象
     * @return 字段描述，如果没有则返回空字符串
     */
    public String extractFieldDescription(Field field) {
        if (field == null) {
            return "";
        }

        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
        if (annotation != null && StringUtils.isNotBlank(annotation.notes())) {
            return annotation.notes();
        }

        return "";
    }

    /**
     * 检查字段是否应该被处理
     * 基于注解存在性和配置进行判断
     *
     * @param field 字段对象
     * @param config 配置对象
     * @return 是否应该处理该字段
     */
    public boolean shouldProcessField(Field field, LogStringConfig config) {
        if (field == null) {
            return false;
        }

        // 检查是否有 @ApiModelProperty 注解
        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
        if (annotation == null) {
            return false;
        }

        // 检查 value 是否为空
        if (StringUtils.isBlank(annotation.value())) {
            return false;
        }

        // 检查是否在忽略字段列表中
        if (config != null && config.isEnableIgnoreFields() && isIgnoreField(field, config.getIgnoreFieldNames())) {
            return false;
        }

        return true;
    }

    /**
     * 检查字段是否标记为只输出值
     * 基于 @OnlyIgnoreKey 注解进行判断
     *
     * @param field 字段对象
     * @return 是否只输出值
     */
    public boolean isOnlyOutputValue(Field field) {
        if (field == null) {
            return false;
        }
        
        return field.getAnnotation(OnlyIgnoreKey.class) != null;
    }

    /**
     * 检查字段是否为忽略字段
     * 基于字段名和配置的忽略字段模式进行判断
     *
     * @param field 字段对象
     * @param ignoreFieldNames 忽略字段名称集合
     * @return 是否为忽略字段
     */
    public boolean isIgnoreField(Field field, Set<String> ignoreFieldNames) {
        if (field == null || ignoreFieldNames == null || ignoreFieldNames.isEmpty()) {
            return false;
        }

        String fieldName = field.getName().toLowerCase();

        // 检查是否匹配忽略字段模式
        for (String ignorePattern : ignoreFieldNames) {
            if (fieldName.equals(ignorePattern.toLowerCase())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 创建 FieldInfo 对象
     *
     * @param field 字段对象
     * @param config 配置对象
     * @return FieldInfo 对象
     */
    public FieldInfo createFieldInfo(Field field, LogStringConfig config) {
        if (field == null) {
            return null;
        }

        String originalName = field.getName();
        String chineseLabel = extractChineseLabel(field);
        Class<?> type = field.getType();

        FieldInfo fieldInfo = new FieldInfo(originalName, chineseLabel, type, field);

        // 设置敏感字段标记
        if (config != null && config.isMaskSensitiveFields()) {
            fieldInfo.setSensitive(isSensitiveField(field, config.getSensitiveFieldNames()));
        }

        // 检查是否为嵌套对象（非基本类型且非常用类型）
        fieldInfo.setNested(isNestedObject(type));
        
        // 设置是否只输出值的标记
        fieldInfo.setOnlyOutputValue(isOnlyOutputValue(field));

        return fieldInfo;
    }

    /**
     * 判断是否为嵌套对象
     *
     * @param type 字段类型
     * @return 是否为嵌套对象
     */
    private boolean isNestedObject(Class<?> type) {
        if (type == null) {
            return false;
        }

        // 基本类型和包装类型
        if (type.isPrimitive() ||
            type == String.class ||
            type == Boolean.class ||
            type == Integer.class ||
            type == Long.class ||
            type == Double.class ||
            type == Float.class ||
            type == Short.class ||
            type == Byte.class ||
            type == Character.class) {
            return false;
        }

        // 日期时间类型
        if (type.getName().startsWith("java.time.") ||
            type.getName().startsWith("java.util.Date") ||
            type.getName().startsWith("java.sql.")) {
            return false;
        }

        // 集合类型
        if (type.getName().startsWith("java.util.") ||
            type.getName().startsWith("java.lang.")) {
            return false;
        }

        // 其他情况认为是嵌套对象
        return true;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bxm</groupId>
    <artifactId>bxm</artifactId>
    <version>1.0.0</version>

    <name>bxm</name>

    <properties>
        <bxm.version>1.0.0</bxm.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.13</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.7</pagehelper.boot.version>
        <druid.version>1.2.16</druid.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>2.0.34</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <transmittable-thread-local.version>2.14.3</transmittable-thread-local.version>
        <nacos.client.version>1.4.1</nacos.client.version>
        <alibaba.druid.version>1.1.22</alibaba.druid.version>
        <mybatis-plus.version>3.4.1</mybatis-plus.version>
        <knife4j.version>3.0.3</knife4j.version>
        <hutool.all.version>5.7.15</hutool.all.version>
        <xxl-job.version>2.4.1</xxl-job.version>
        <poi.version>4.1.2</poi.version>
        <aliyun.oss.version>3.8.0</aliyun.oss.version>
        <junrar.version>4.0.0</junrar.version>
        <commons.compress.version>1.21</commons.compress.version>
        <sevenzipjbinding.version>16.02-2.01</sevenzipjbinding.version>
        <sevenzipjbinding.all.version>16.02-2.01</sevenzipjbinding.all.version>
        <canal.client.version>1.1.3</canal.client.version>
        <bxm.common.core.version>2.0.8-SNAPSHOT</bxm.common.core.version>
        <bxm.common.datascope.version>1.0.2-RELEASE</bxm.common.datascope.version>
        <bxm.common.datasource.version>1.0.1-RELEASE</bxm.common.datasource.version>
        <bxm.common.log.version>1.1.0-RELEASE</bxm.common.log.version>
        <bxm.common.mybatisplus.version>1.0.3-RELEASE</bxm.common.mybatisplus.version>
        <bxm.common.security.version>1.0.4-RELEASE</bxm.common.security.version>
        <bxm.common.redis.version>1.0.5-RELEASE</bxm.common.redis.version>
        <bxm.common.swagger.version>1.0.1-RELEASE</bxm.common.swagger.version>
        <bxm.common.rocketmq.version>1.0.0-RELEASE</bxm.common.rocketmq.version>
        <bxm.common.customize.version>1.0.0-RELEASE</bxm.common.customize.version>
        <bxm.api.system.version>1.3.0-SNAPSHOT</bxm.api.system.version>
        <bxm.api.customer.version>1.8.2-SNAPSHOT</bxm.api.customer.version>
        <bxm.api.thirdpart.version>1.3.3-SNAPSHOT</bxm.api.thirdpart.version>
        <bxm.api.file.version>1.1.8-SNAPSHOT</bxm.api.file.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- Hutool 工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.all.version}</version>
            </dependency>

            <!-- JunRAR 解压工具 -->
            <dependency>
                <groupId>com.github.junrar</groupId>
                <artifactId>junrar</artifactId>
                <version>${junrar.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-common-core</artifactId>
                <version>${bxm.common.core.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-common-swagger</artifactId>
                <version>${bxm.common.swagger.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-common-security</artifactId>
                <version>${bxm.common.security.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-common-datascope</artifactId>
                <version>${bxm.common.datascope.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-common-datasource</artifactId>
                <version>${bxm.common.datasource.version}</version>
            </dependency>

            <!-- 分布式事务 -->
<!--            <dependency>-->
<!--                <groupId>com.bxm</groupId>-->
<!--                <artifactId>bxm-common-seata</artifactId>-->
<!--                <version>${bxm.common.seata.version}</version>-->
<!--            </dependency>-->

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-common-log</artifactId>
                <version>${bxm.common.log.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-common-redis</artifactId>
                <version>${bxm.common.redis.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-api-system</artifactId>
                <version>${bxm.api.system.version}</version>
            </dependency>

            <!-- mybatis-plus -->
            <dependency>
                <groupId>com.bxm</groupId>
                <artifactId>bxm-common-mybatisplus</artifactId>
                <version>${bxm.common.mybatisplus.version}</version>
            </dependency>

            <!-- hutool 工具类库 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.all.version}</version>
            </dependency>

            <!-- junrar RAR解压工具 -->
            <dependency>
                <groupId>com.github.junrar</groupId>
                <artifactId>junrar</artifactId>
                <version>${junrar.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>bxm-auth</module>
        <module>bxm-gateway</module>
        <module>bxm-visual</module>
        <module>bxm-modules</module>
        <module>bxm-api</module>
        <module>bxm-common</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>http://120.27.156.42:18081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://120.27.156.42:18081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
